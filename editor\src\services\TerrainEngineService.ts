/**
 * 地形引擎集成服务
 * 负责编辑器与底层地形引擎的集成
 */
import { TerrainComponent, TerrainSystem, TerrainGenerationAlgorithms } from '../libs/dl-engine.mjs';
import { store } from '../store';
import { updateTerrainComponent, addOperation, TerrainOperationType } from '../store/terrain/terrainSlice';
import { updateEntity } from '../store/scene/sceneSlice';

/**
 * 地形创建选项
 */
export interface TerrainCreationOptions {
  width?: number;
  height?: number;
  resolution?: number;
  maxHeight?: number;
  useLOD?: boolean;
  usePhysics?: boolean;
  layers?: any[];
}

/**
 * 地形雕刻选项
 */
export interface TerrainSculptingOptions {
  brushType: string;
  brushShape: string;
  brushSize: number;
  brushStrength: number;
  brushFalloff: number;
  targetHeight?: number;
  position: { x: number; z: number };
}

/**
 * 地形引擎集成服务
 */
export class TerrainEngineService {
  private static instance: TerrainEngineService;
  private terrainSystem: TerrainSystem | null = null;
  private terrainComponents: Map<string, TerrainComponent> = new Map();

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): TerrainEngineService {
    if (!TerrainEngineService.instance) {
      TerrainEngineService.instance = new TerrainEngineService();
    }
    return TerrainEngineService.instance;
  }

  /**
   * 初始化地形系统
   * @param terrainSystem 地形系统实例
   */
  public initialize(terrainSystem: TerrainSystem): void {
    this.terrainSystem = terrainSystem;
  }

  /**
   * 创建地形组件
   * @param entityId 实体ID
   * @param options 创建选项
   * @returns 创建的地形组件
   */
  public async createTerrainComponent(entityId: string, options: TerrainCreationOptions = {}): Promise<TerrainComponent> {
    try {
      // 创建地形组件
      const terrainComponent = new TerrainComponent({
        width: options.width || 1000,
        height: options.height || 1000,
        resolution: options.resolution || 256,
        maxHeight: options.maxHeight || 100,
        useLOD: options.useLOD || false,
        usePhysics: options.usePhysics || false,
        layers: options.layers || []
      });

      // 存储地形组件
      this.terrainComponents.set(entityId, terrainComponent);

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 更新实体组件
      store.dispatch(updateEntity({
        id: entityId,
        components: {
          TerrainComponent: this.serializeTerrainComponent(terrainComponent)
        }
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.CREATE,
        entityId,
        data: options,
        description: '创建地形组件'
      }));

      return terrainComponent;
    } catch (error) {
      console.error('创建地形组件失败:', error);
      throw error;
    }
  }

  /**
   * 获取地形组件
   * @param entityId 实体ID
   * @returns 地形组件
   */
  public getTerrainComponent(entityId: string): TerrainComponent | null {
    return this.terrainComponents.get(entityId) || null;
  }

  /**
   * 雕刻地形
   * @param entityId 实体ID
   * @param options 雕刻选项
   */
  public async sculptTerrain(entityId: string, options: TerrainSculptingOptions): Promise<void> {
    try {
      const terrainComponent = this.terrainComponents.get(entityId);
      if (!terrainComponent) {
        throw new Error(`地形组件不存在: ${entityId}`);
      }

      // 执行雕刻操作
      await this.performSculptingOperation(terrainComponent, options);

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.SCULPT,
        entityId,
        data: options,
        description: `地形雕刻: ${options.brushType}`
      }));
    } catch (error) {
      console.error('地形雕刻失败:', error);
      throw error;
    }
  }

  /**
   * 生成地形
   * @param entityId 实体ID
   * @param algorithm 生成算法
   * @param params 算法参数
   */
  public async generateTerrain(entityId: string, algorithm: string, params: any): Promise<void> {
    try {
      const terrainComponent = this.terrainComponents.get(entityId);
      if (!terrainComponent) {
        throw new Error(`地形组件不存在: ${entityId}`);
      }

      // 调用地形生成算法
      await TerrainGenerationAlgorithms.generateTerrain(terrainComponent, {
        baseTerrainType: algorithm,
        baseTerrainParams: params,
        features: [],
        seed: params.seed || Math.random() * 1000
      });

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.GENERATE,
        entityId,
        data: { algorithm, params },
        description: `生成地形: ${algorithm}`
      }));
    } catch (error) {
      console.error('地形生成失败:', error);
      throw error;
    }
  }

  /**
   * 导入高度图
   * @param entityId 实体ID
   * @param file 高度图文件
   * @param options 导入选项
   */
  public async importHeightMap(entityId: string, file: File, options: any = {}): Promise<void> {
    try {
      const terrainComponent = this.terrainComponents.get(entityId);
      if (!terrainComponent) {
        throw new Error(`地形组件不存在: ${entityId}`);
      }

      // 读取文件
      const arrayBuffer = await file.arrayBuffer();
      
      // 导入高度图
      const success = await terrainComponent.importFromHeightMap(arrayBuffer, {
        format: this.getHeightMapFormat(file.name),
        computeNormals: true,
        applySmoothing: options.applySmoothing || false,
        ...options
      });

      if (!success) {
        throw new Error('高度图导入失败');
      }

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.IMPORT,
        entityId,
        data: { fileName: file.name, options },
        description: `导入高度图: ${file.name}`
      }));
    } catch (error) {
      console.error('导入高度图失败:', error);
      throw error;
    }
  }

  /**
   * 更新地形物理设置
   * @param entityId 实体ID
   * @param physicsSettings 物理设置
   */
  public updateTerrainPhysics(entityId: string, physicsSettings: any): void {
    try {
      const terrainComponent = this.terrainComponents.get(entityId);
      if (!terrainComponent) {
        throw new Error(`地形组件不存在: ${entityId}`);
      }

      // 更新物理设置
      Object.assign(terrainComponent, physicsSettings);
      terrainComponent.needsPhysicsUpdate = true;

      // 更新Redux状态
      store.dispatch(updateTerrainComponent({
        entityId,
        component: this.serializeTerrainComponent(terrainComponent)
      }));

      // 记录操作
      store.dispatch(addOperation({
        type: TerrainOperationType.PHYSICS,
        entityId,
        data: physicsSettings,
        description: '更新地形物理设置'
      }));
    } catch (error) {
      console.error('更新地形物理设置失败:', error);
      throw error;
    }
  }

  /**
   * 执行雕刻操作
   * @param terrainComponent 地形组件
   * @param options 雕刻选项
   */
  private async performSculptingOperation(terrainComponent: TerrainComponent, options: TerrainSculptingOptions): Promise<void> {
    const { brushType, brushSize, brushStrength, position } = options;

    // 计算影响区域
    const radius = brushSize / 2;
    const centerX = Math.floor((position.x + terrainComponent.width / 2) / terrainComponent.width * terrainComponent.resolution);
    const centerZ = Math.floor((position.z + terrainComponent.height / 2) / terrainComponent.height * terrainComponent.resolution);

    // 应用笔刷效果
    for (let z = Math.max(0, centerZ - radius); z <= Math.min(terrainComponent.resolution - 1, centerZ + radius); z++) {
      for (let x = Math.max(0, centerX - radius); x <= Math.min(terrainComponent.resolution - 1, centerX + radius); x++) {
        const dx = x - centerX;
        const dz = z - centerZ;
        const distance = Math.sqrt(dx * dx + dz * dz);

        if (distance <= radius) {
          const index = z * terrainComponent.resolution + x;
          const falloff = Math.pow(1 - distance / radius, options.brushFalloff);
          const delta = brushStrength * falloff;

          switch (brushType) {
            case 'raise':
              terrainComponent.heightData[index] += delta;
              break;
            case 'lower':
              terrainComponent.heightData[index] -= delta;
              break;
            case 'flatten':
              const targetHeight = options.targetHeight || 0.5;
              terrainComponent.heightData[index] = terrainComponent.heightData[index] * (1 - delta) + targetHeight * delta;
              break;
            case 'smooth':
              // 简化的平滑算法
              let sum = 0;
              let count = 0;
              for (let oz = -1; oz <= 1; oz++) {
                for (let ox = -1; ox <= 1; ox++) {
                  const nx = x + ox;
                  const nz = z + oz;
                  if (nx >= 0 && nx < terrainComponent.resolution && nz >= 0 && nz < terrainComponent.resolution) {
                    sum += terrainComponent.heightData[nz * terrainComponent.resolution + nx];
                    count++;
                  }
                }
              }
              const average = sum / count;
              terrainComponent.heightData[index] = terrainComponent.heightData[index] * (1 - delta) + average * delta;
              break;
          }

          // 确保高度在有效范围内
          terrainComponent.heightData[index] = Math.max(0, Math.min(1, terrainComponent.heightData[index]));
        }
      }
    }

    // 标记需要更新
    terrainComponent.needsUpdate = true;
  }

  /**
   * 序列化地形组件
   * @param terrainComponent 地形组件
   * @returns 序列化后的数据
   */
  private serializeTerrainComponent(terrainComponent: TerrainComponent): any {
    return {
      width: terrainComponent.width,
      height: terrainComponent.height,
      resolution: terrainComponent.resolution,
      maxHeight: terrainComponent.maxHeight,
      useLOD: terrainComponent.useLOD,
      usePhysics: terrainComponent.usePhysics,
      layers: terrainComponent.layers,
      needsUpdate: terrainComponent.needsUpdate,
      needsPhysicsUpdate: terrainComponent.needsPhysicsUpdate,
      // 注意：heightData 很大，可能需要特殊处理
      heightDataChecksum: this.calculateChecksum(terrainComponent.heightData)
    };
  }

  /**
   * 获取高度图格式
   * @param fileName 文件名
   * @returns 格式
   */
  private getHeightMapFormat(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'png':
        return 'png';
      case 'jpg':
      case 'jpeg':
        return 'jpeg';
      case 'raw':
        return 'raw';
      case 'r16':
        return 'r16';
      case 'r32':
        return 'r32';
      default:
        return 'png';
    }
  }

  /**
   * 计算数据校验和
   * @param data 数据
   * @returns 校验和
   */
  private calculateChecksum(data: Float32Array): string {
    let sum = 0;
    for (let i = 0; i < data.length; i += 100) { // 采样计算以提高性能
      sum += data[i];
    }
    return sum.toString(36);
  }
}

// 导出单例实例
export const terrainEngineService = TerrainEngineService.getInstance();
