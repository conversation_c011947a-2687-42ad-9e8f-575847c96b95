/**
 * 地形状态切片
 */
import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit';
import { TerrainComponent } from '../../../libs/dl-engine.mjs';

// 地形操作类型
export enum TerrainOperationType {
  CREATE = 'create',
  SCULPT = 'sculpt',
  TEXTURE = 'texture',
  PHYSICS = 'physics',
  IMPORT = 'import',
  EXPORT = 'export',
  GENERATE = 'generate'
}

// 地形操作记录
export interface TerrainOperation {
  id: string;
  type: TerrainOperationType;
  entityId: string;
  timestamp: number;
  data: any;
  description: string;
}

// 地形预览设置
export interface TerrainPreviewSettings {
  enabled: boolean;
  algorithm: string;
  params: any;
  resolution: number;
  width: number;
  height: number;
}

// 地形编辑设置
export interface TerrainEditSettings {
  brushType: string;
  brushShape: string;
  brushSize: number;
  brushStrength: number;
  brushFalloff: number;
  targetHeight: number;
  showBrushPreview: boolean;
  showHeightContours: boolean;
  showSlopeOverlay: boolean;
}

// 地形状态接口
export interface TerrainState {
  // 当前选中的地形实体ID
  selectedTerrainId: string | null;
  // 地形组件数据映射
  terrainComponents: Record<string, any>;
  // 操作历史
  operationHistory: TerrainOperation[];
  // 撤销栈
  undoStack: TerrainOperation[];
  // 重做栈
  redoStack: TerrainOperation[];
  // 预览设置
  previewSettings: TerrainPreviewSettings;
  // 编辑设置
  editSettings: TerrainEditSettings;
  // 是否正在生成地形
  isGenerating: boolean;
  // 是否正在导入/导出
  isImporting: boolean;
  isExporting: boolean;
  // 错误信息
  error: string | null;
  // 性能统计
  performanceStats: {
    lastUpdateTime: number;
    triangleCount: number;
    vertexCount: number;
    chunkCount: number;
  };
}

// 初始状态
const initialState: TerrainState = {
  selectedTerrainId: null,
  terrainComponents: {},
  operationHistory: [],
  undoStack: [],
  redoStack: [],
  previewSettings: {
    enabled: true,
    algorithm: 'perlin',
    params: {},
    resolution: 64,
    width: 300,
    height: 200
  },
  editSettings: {
    brushType: 'raise',
    brushShape: 'circle',
    brushSize: 10,
    brushStrength: 0.5,
    brushFalloff: 2,
    targetHeight: 0.5,
    showBrushPreview: true,
    showHeightContours: false,
    showSlopeOverlay: false
  },
  isGenerating: false,
  isImporting: false,
  isExporting: false,
  error: null,
  performanceStats: {
    lastUpdateTime: 0,
    triangleCount: 0,
    vertexCount: 0,
    chunkCount: 0
  }
};

// 异步操作：生成地形
export const generateTerrain = createAsyncThunk(
  'terrain/generateTerrain',
  async (params: { entityId: string; algorithm: string; options: any }, { rejectWithValue }) => {
    try {
      // 这里将来会调用引擎的地形生成功能
      // const result = await EngineService.generateTerrain(params.entityId, params.algorithm, params.options);
      // 暂时返回模拟数据
      return {
        entityId: params.entityId,
        success: true,
        data: params.options
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 异步操作：导入地形
export const importTerrain = createAsyncThunk(
  'terrain/importTerrain',
  async (params: { entityId: string; file: File; options: any }, { rejectWithValue }) => {
    try {
      // 这里将来会调用引擎的地形导入功能
      // const result = await EngineService.importTerrain(params.entityId, params.file, params.options);
      // 暂时返回模拟数据
      return {
        entityId: params.entityId,
        success: true,
        fileName: params.file.name
      };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

// 创建地形切片
const terrainSlice = createSlice({
  name: 'terrain',
  initialState,
  reducers: {
    // 选择地形
    selectTerrain: (state, action: PayloadAction<string | null>) => {
      state.selectedTerrainId = action.payload;
    },

    // 更新地形组件
    updateTerrainComponent: (state, action: PayloadAction<{ entityId: string; component: any }>) => {
      const { entityId, component } = action.payload;
      state.terrainComponents[entityId] = component;
    },

    // 添加操作记录
    addOperation: (state, action: PayloadAction<Omit<TerrainOperation, 'id' | 'timestamp'>>) => {
      const operation: TerrainOperation = {
        ...action.payload,
        id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now()
      };
      
      state.operationHistory.push(operation);
      state.undoStack.push(operation);
      state.redoStack = []; // 清空重做栈
    },

    // 撤销操作
    undo: (state) => {
      if (state.undoStack.length > 0) {
        const operation = state.undoStack.pop()!;
        state.redoStack.push(operation);
      }
    },

    // 重做操作
    redo: (state) => {
      if (state.redoStack.length > 0) {
        const operation = state.redoStack.pop()!;
        state.undoStack.push(operation);
      }
    },

    // 更新预览设置
    updatePreviewSettings: (state, action: PayloadAction<Partial<TerrainPreviewSettings>>) => {
      state.previewSettings = { ...state.previewSettings, ...action.payload };
    },

    // 更新编辑设置
    updateEditSettings: (state, action: PayloadAction<Partial<TerrainEditSettings>>) => {
      state.editSettings = { ...state.editSettings, ...action.payload };
    },

    // 更新性能统计
    updatePerformanceStats: (state, action: PayloadAction<Partial<typeof initialState.performanceStats>>) => {
      state.performanceStats = { ...state.performanceStats, ...action.payload };
    },

    // 清除错误
    clearError: (state) => {
      state.error = null;
    },

    // 重置状态
    resetTerrain: (state) => {
      return { ...initialState };
    }
  },
  extraReducers: (builder) => {
    // 生成地形
    builder
      .addCase(generateTerrain.pending, (state) => {
        state.isGenerating = true;
        state.error = null;
      })
      .addCase(generateTerrain.fulfilled, (state, action) => {
        state.isGenerating = false;
        // 添加生成操作记录
        const operation: TerrainOperation = {
          id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: TerrainOperationType.GENERATE,
          entityId: action.payload.entityId,
          timestamp: Date.now(),
          data: action.payload.data,
          description: '地形生成'
        };
        state.operationHistory.push(operation);
        state.undoStack.push(operation);
        state.redoStack = [];
      })
      .addCase(generateTerrain.rejected, (state, action) => {
        state.isGenerating = false;
        state.error = action.payload as string;
      });

    // 导入地形
    builder
      .addCase(importTerrain.pending, (state) => {
        state.isImporting = true;
        state.error = null;
      })
      .addCase(importTerrain.fulfilled, (state, action) => {
        state.isImporting = false;
        // 添加导入操作记录
        const operation: TerrainOperation = {
          id: `imp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: TerrainOperationType.IMPORT,
          entityId: action.payload.entityId,
          timestamp: Date.now(),
          data: { fileName: action.payload.fileName },
          description: `导入地形: ${action.payload.fileName}`
        };
        state.operationHistory.push(operation);
        state.undoStack.push(operation);
        state.redoStack = [];
      })
      .addCase(importTerrain.rejected, (state, action) => {
        state.isImporting = false;
        state.error = action.payload as string;
      });
  }
});

// 导出actions
export const {
  selectTerrain,
  updateTerrainComponent,
  addOperation,
  undo,
  redo,
  updatePreviewSettings,
  updateEditSettings,
  updatePerformanceStats,
  clearError,
  resetTerrain
} = terrainSlice.actions;

// 导出reducer
export default terrainSlice.reducer;
